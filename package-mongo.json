{"name": "mongo-batch-insert-server", "version": "1.0.0", "description": "Node.js服务用于MongoDB批量数据插入", "main": "mongo-batch-insert-server.js", "scripts": {"start": "node mongo-batch-insert-server.js", "dev": "nodemon mongo-batch-insert-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mongodb", "batch-insert", "node.js", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongodb": "^6.3.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}