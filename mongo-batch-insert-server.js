const express = require('express');
const { MongoClient } = require('mongodb');
const app = express();

// 中间件配置
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true }));

// MongoDB连接配置
const MONGO_URL = process.env.MONGO_URL || 'mongodb://localhost:27017';
const DB_NAME = process.env.DB_NAME || 'your_database';
const COLLECTION_NAME = process.env.COLLECTION_NAME || 'your_collection';

let db;
let client;

// 连接MongoDB
async function connectMongoDB() {
    try {
        client = new MongoClient(MONGO_URL);
        await client.connect();
        db = client.db(DB_NAME);
        console.log('MongoDB连接成功');
        console.log(`数据库: ${DB_NAME}`);
        console.log(`集合: ${COLLECTION_NAME}`);
    } catch (error) {
        console.error('MongoDB连接失败:', error);
        process.exit(1);
    }
}

// 数据验证函数
function validateData(data) {
    if (!Array.isArray(data)) {
        return { valid: false, message: '数据必须是数组格式' };
    }

    for (let i = 0; i < data.length; i++) {
        const item = data[i];
        
        // 检查必需字段
        if (!item.consult_id || typeof item.consult_id !== 'string') {
            return { valid: false, message: `第${i + 1}条数据的consult_id字段无效` };
        }
        
        if (!item.user_id || typeof item.user_id !== 'string') {
            return { valid: false, message: `第${i + 1}条数据的user_id字段无效` };
        }
        
        if (typeof item.source !== 'number') {
            return { valid: false, message: `第${i + 1}条数据的source字段必须是数字` };
        }
        
        if (typeof item.del_flag !== 'number') {
            return { valid: false, message: `第${i + 1}条数据的del_flag字段必须是数字` };
        }
    }
    
    return { valid: true };
}

// 数据预处理函数
function preprocessData(data) {
    return data.map(item => ({
        consult_id: item.consult_id,
        user_id: item.user_id,
        source: item.source,
        create_time: item.create_time ? new Date(item.create_time) : new Date(),
        update_time: item.update_time ? new Date(item.update_time) : new Date(),
        del_flag: item.del_flag
    }));
}

// 批量插入数据接口
app.post('/api/batch-insert', async (req, res) => {
    try {
        const { data, collection } = req.body;
        
        if (!data) {
            return res.status(400).json({
                success: false,
                message: '请提供要插入的数据'
            });
        }

        // 数据验证
        const validation = validateData(data);
        if (!validation.valid) {
            return res.status(400).json({
                success: false,
                message: validation.message
            });
        }

        // 数据预处理
        const processedData = preprocessData(data);
        
        // 使用指定的集合名称或默认集合名称
        const targetCollection = collection || COLLECTION_NAME;
        const mongoCollection = db.collection(targetCollection);
        
        // 批量插入数据
        const result = await mongoCollection.insertMany(processedData);
        
        console.log(`成功插入 ${result.insertedCount} 条数据到集合 ${targetCollection}`);
        
        res.json({
            success: true,
            message: `成功插入 ${result.insertedCount} 条数据`,
            insertedCount: result.insertedCount,
            insertedIds: result.insertedIds,
            collection: targetCollection
        });
        
    } catch (error) {
        console.error('批量插入数据失败:', error);
        res.status(500).json({
            success: false,
            message: '批量插入数据失败',
            error: error.message
        });
    }
});

// 查询数据接口
app.get('/api/data/:collection?', async (req, res) => {
    try {
        const collection = req.params.collection || COLLECTION_NAME;
        const { limit = 10, skip = 0 } = req.query;
        
        const mongoCollection = db.collection(collection);
        const data = await mongoCollection
            .find({})
            .skip(parseInt(skip))
            .limit(parseInt(limit))
            .toArray();
            
        const total = await mongoCollection.countDocuments();
        
        res.json({
            success: true,
            data,
            total,
            collection,
            pagination: {
                skip: parseInt(skip),
                limit: parseInt(limit)
            }
        });
        
    } catch (error) {
        console.error('查询数据失败:', error);
        res.status(500).json({
            success: false,
            message: '查询数据失败',
            error: error.message
        });
    }
});

// 删除集合中所有数据接口
app.delete('/api/clear/:collection?', async (req, res) => {
    try {
        const collection = req.params.collection || COLLECTION_NAME;
        const mongoCollection = db.collection(collection);
        
        const result = await mongoCollection.deleteMany({});
        
        console.log(`清空集合 ${collection}，删除了 ${result.deletedCount} 条数据`);
        
        res.json({
            success: true,
            message: `成功清空集合 ${collection}`,
            deletedCount: result.deletedCount,
            collection
        });
        
    } catch (error) {
        console.error('清空数据失败:', error);
        res.status(500).json({
            success: false,
            message: '清空数据失败',
            error: error.message
        });
    }
});

// 健康检查接口
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: DB_NAME,
        collection: COLLECTION_NAME
    });
});

// 获取数据库状态
app.get('/api/status', async (req, res) => {
    try {
        const collections = await db.listCollections().toArray();
        const stats = {};
        
        for (const collection of collections) {
            const count = await db.collection(collection.name).countDocuments();
            stats[collection.name] = count;
        }
        
        res.json({
            success: true,
            database: DB_NAME,
            collections: stats
        });
        
    } catch (error) {
        console.error('获取数据库状态失败:', error);
        res.status(500).json({
            success: false,
            message: '获取数据库状态失败',
            error: error.message
        });
    }
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: error.message
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('正在关闭服务器...');
    if (client) {
        await client.close();
        console.log('MongoDB连接已关闭');
    }
    process.exit(0);
});

// 启动服务器
const PORT = process.env.PORT || 3000;

async function startServer() {
    await connectMongoDB();
    
    app.listen(PORT, () => {
        console.log(`服务器运行在端口 ${PORT}`);
        console.log(`健康检查: http://localhost:${PORT}/health`);
        console.log(`数据库状态: http://localhost:${PORT}/api/status`);
        console.log(`批量插入接口: POST http://localhost:${PORT}/api/batch-insert`);
        console.log(`查询数据接口: GET http://localhost:${PORT}/api/data`);
    });
}

startServer().catch(console.error);
