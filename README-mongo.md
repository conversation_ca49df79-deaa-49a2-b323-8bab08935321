# MongoDB批量插入服务

这是一个Node.js服务，用于连接MongoDB数据库并批量插入数据。

## 功能特性

- ✅ 连接MongoDB数据库
- ✅ 批量插入数据到指定集合
- ✅ 数据验证和预处理
- ✅ 查询数据接口
- ✅ 清空集合数据
- ✅ 健康检查和状态监控
- ✅ 错误处理和日志记录

## 安装和运行

### 1. 安装依赖

```bash
# 使用提供的package.json文件
cp package-mongo.json package.json
npm install
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置你的MongoDB连接信息
```

### 3. 启动服务

```bash
# 生产环境
npm start

# 开发环境（需要安装nodemon）
npm run dev
```

## API接口

### 1. 批量插入数据

**POST** `/api/batch-insert`

请求体：
```json
{
  "data": [
    {
      "consult_id": "6847fd799f8e2b56716c40b0",
      "user_id": "636a37da21f36a3de91e8392",
      "source": 0,
      "create_time": "2025-06-26T12:28:37.08Z",
      "update_time": "2025-06-26T12:28:37.08Z",
      "del_flag": 0
    }
  ],
  "collection": "your_collection_name" // 可选，指定集合名称
}
```

响应：
```json
{
  "success": true,
  "message": "成功插入 1 条数据",
  "insertedCount": 1,
  "insertedIds": {...},
  "collection": "your_collection_name"
}
```

### 2. 查询数据

**GET** `/api/data/:collection?limit=10&skip=0`

响应：
```json
{
  "success": true,
  "data": [...],
  "total": 100,
  "collection": "your_collection_name",
  "pagination": {
    "skip": 0,
    "limit": 10
  }
}
```

### 3. 清空集合数据

**DELETE** `/api/clear/:collection`

响应：
```json
{
  "success": true,
  "message": "成功清空集合 your_collection_name",
  "deletedCount": 10,
  "collection": "your_collection_name"
}
```

### 4. 健康检查

**GET** `/health`

响应：
```json
{
  "status": "ok",
  "timestamp": "2025-06-27T...",
  "database": "your_database",
  "collection": "your_collection"
}
```

### 5. 数据库状态

**GET** `/api/status`

响应：
```json
{
  "success": true,
  "database": "your_database",
  "collections": {
    "collection1": 100,
    "collection2": 50
  }
}
```

## 数据格式要求

插入的数据必须包含以下字段：

- `consult_id`: 字符串类型，咨询ID
- `user_id`: 字符串类型，用户ID  
- `source`: 数字类型，来源标识
- `create_time`: 日期类型（可选，默认当前时间）
- `update_time`: 日期类型（可选，默认当前时间）
- `del_flag`: 数字类型，删除标识

## 测试

运行测试脚本：

```bash
# 确保服务器已启动
npm start

# 在另一个终端运行测试
node test-batch-insert.js
```

测试脚本会：
1. 检查服务器健康状态
2. 获取数据库状态
3. 批量插入测试数据
4. 查询插入的数据
5. 测试无效数据处理
6. 可选择清空测试数据

## 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| MONGO_URL | mongodb://localhost:27017 | MongoDB连接URL |
| DB_NAME | your_database | 数据库名称 |
| COLLECTION_NAME | your_collection | 默认集合名称 |
| PORT | 3000 | 服务器端口 |

## 错误处理

服务包含完整的错误处理机制：

- 数据验证错误
- MongoDB连接错误
- 插入操作错误
- 服务器内部错误

所有错误都会返回统一的JSON格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 注意事项

1. 确保MongoDB服务已启动
2. 检查网络连接和防火墙设置
3. 大批量数据插入时注意内存使用
4. 生产环境建议使用连接池和集群配置
5. 定期备份重要数据

## 许可证

MIT License
