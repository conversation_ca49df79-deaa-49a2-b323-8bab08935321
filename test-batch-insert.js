const axios = require('axios');

// 服务器地址
const SERVER_URL = 'http://localhost:3000';

// 测试数据
const testData = [
    {
        "consult_id": "6847fd799f8e2b56716c40b0",
        "user_id": "636a37da21f36a3de91e8392",
        "source": 0,
        "create_time": "2025-06-26T12:28:37.08Z",
        "update_time": "2025-06-26T12:28:37.08Z",
        "del_flag": 0
    },
    {
        "consult_id": "6847fd799f8e2b56716c40b1",
        "user_id": "636a37da21f36a3de91e8393",
        "source": 0,
        "create_time": "2025-06-26T12:28:37.08Z",
        "update_time": "2025-06-26T12:28:37.08Z",
        "del_flag": 0
    },
    {
        "consult_id": "6847fd799f8e2b56716c40b2",
        "user_id": "636a37da21f36a3de91e8394",
        "source": 1,
        "create_time": "2025-06-26T12:30:00.00Z",
        "update_time": "2025-06-26T12:30:00.00Z",
        "del_flag": 0
    }
];

// 测试函数
async function testBatchInsert() {
    try {
        console.log('开始测试批量插入...');
        
        // 1. 检查服务器健康状态
        console.log('\n1. 检查服务器状态...');
        const healthResponse = await axios.get(`${SERVER_URL}/health`);
        console.log('服务器状态:', healthResponse.data);
        
        // 2. 获取数据库状态
        console.log('\n2. 获取数据库状态...');
        const statusResponse = await axios.get(`${SERVER_URL}/api/status`);
        console.log('数据库状态:', statusResponse.data);
        
        // 3. 批量插入数据
        console.log('\n3. 批量插入数据...');
        const insertResponse = await axios.post(`${SERVER_URL}/api/batch-insert`, {
            data: testData,
            collection: 'test_collection' // 可选，指定集合名称
        });
        console.log('插入结果:', insertResponse.data);
        
        // 4. 查询插入的数据
        console.log('\n4. 查询插入的数据...');
        const queryResponse = await axios.get(`${SERVER_URL}/api/data/test_collection?limit=5`);
        console.log('查询结果:', queryResponse.data);
        
        // 5. 再次获取数据库状态
        console.log('\n5. 再次获取数据库状态...');
        const statusResponse2 = await axios.get(`${SERVER_URL}/api/status`);
        console.log('更新后的数据库状态:', statusResponse2.data);
        
        console.log('\n✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
    }
}

// 测试清空数据功能
async function testClearData() {
    try {
        console.log('\n开始测试清空数据...');
        
        const clearResponse = await axios.delete(`${SERVER_URL}/api/clear/test_collection`);
        console.log('清空结果:', clearResponse.data);
        
        console.log('✅ 清空数据测试完成！');
        
    } catch (error) {
        console.error('❌ 清空数据测试失败:', error.response?.data || error.message);
    }
}

// 错误数据测试
async function testInvalidData() {
    try {
        console.log('\n开始测试无效数据...');
        
        const invalidData = [
            {
                "consult_id": "", // 无效的consult_id
                "user_id": "636a37da21f36a3de91e8392",
                "source": 0,
                "del_flag": 0
            }
        ];
        
        const response = await axios.post(`${SERVER_URL}/api/batch-insert`, {
            data: invalidData
        });
        console.log('意外成功:', response.data);
        
    } catch (error) {
        console.log('✅ 正确捕获到错误:', error.response?.data || error.message);
    }
}

// 主函数
async function main() {
    console.log('🚀 开始MongoDB批量插入服务测试');
    console.log('请确保服务器已启动在 http://localhost:3000');
    
    // 等待用户确认
    await new Promise(resolve => {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        rl.question('按回车键开始测试...', () => {
            rl.close();
            resolve();
        });
    });
    
    await testBatchInsert();
    await testInvalidData();
    
    // 询问是否清空测试数据
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('\n是否清空测试数据？(y/n): ', async (answer) => {
        if (answer.toLowerCase() === 'y') {
            await testClearData();
        }
        rl.close();
        console.log('测试结束');
    });
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testBatchInsert,
    testClearData,
    testInvalidData
};
